from typing import List, Dict, Any, AsyncGenerator, Optional
import sys
import os
import time
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from prompts.car_qa_prompt import (
    car_qa_rerank_prompt,
    car_qa_sys_prompt,
    car_qa_user_prompt,
    car_qa_strict_empty_sys_prompt,
    car_qa_strict_nonempty_sys_prompt,
    car_qa_common_empty_sys_prompt,
    car_qa_common_nonempty_sys_prompt,
    car_qa_strict_empty_user_prompt,
    car_qa_strict_nonempty_user_prompt,
    car_qa_common_empty_user_prompt,
    car_qa_common_nonempty_user_prompt
)
from services.search_service import SearchService
from services.rerank_service import RerankService
from services.query_rewrite_service import QueryRewriteService
from utils.token_limiter import CharacterLimiter
from config.model_config import get_model_max_input_chars

from loguru import logger
from config.logging_config import configure_logging
from config.car_search_config import CAR_SEARCH_MODEL_CONFIG, CAR_SEARCH_COLLECTIONS
from config.car_search_config import CAR_RERANK_MODEL_CONFIG
configure_logging()
import json
import asyncio


class CARQA:
    """RAG问答类，支持流式和非流式输出，面向汽车领域"""
    
    def __init__(self, model_id: str, request_id: str = None):
        """
        初始化RAG问答实例

        Args:
            model_id: 模型ID (e.g. "gpt_4o", "qwen3_32b", "qwen3_235b_2507")
            request_id: 可选请求ID
        """
        self.model_id = model_id
        self.request_id = request_id
        # 实例化搜索服务
        self.search_service = SearchService(config = CAR_SEARCH_MODEL_CONFIG, request_id=request_id, knowledge="car")
        # 实例化重排服务
        self.rerank_service = RerankService(config = CAR_RERANK_MODEL_CONFIG, request_id=request_id)
        # 实例化查询改写服务
        self.query_rewrite_service = QueryRewriteService(request_id=request_id)
        # 根据模型ID获取最大输入字数并实例化字符数限制器
        max_input_chars = get_model_max_input_chars(model_id)
        self.char_limiter = CharacterLimiter(max_chars=max_input_chars)
        self.logger = logger.bind(request_id=request_id)
        self.logger.info(f"CARQA instance initialized with model_id: {model_id}, 最大输入字数: {max_input_chars}")

    def format_retrieved_docs(self, retrieved_docs: List[Any]) -> List[Any]:
        """格式化检索到的文档"""
        formated_retrieved_docs = []
        for doc in retrieved_docs:
            # print(f"doc: {doc}")
            data = {
                "title": "",
                "content": "",
                "docName": "",
                "docUrl": "",
                "sheetName": "",
                "owner": "",
                "update_time": "",
                "publish_time": "",
                "doc_type": "",
                "project_area": "",
                "score": 0.0,
                "reranker_score": 0.0,
                "collection_name": ""
            }
            docUrl1 = doc.get("metadata_json", {}).get("doc_url", "")
            docUrl2 = doc.get("metadata_json", {}).get("url", "")
            docUrl = docUrl1 if docUrl1 else docUrl2
            docUrl = str(docUrl)
            # print(f"docUrl: {docUrl}")
            title1 = doc.get("metadata_json", {}).get("doc_name", "")
            title2 = doc.get("metadata_json", {}).get("title", "")
            # print(f"title1: {title1}")
            # print(f"title2: {title2}")
            title = title1 if title1 else title2
            title = str(title)
            # print(f"title: {title}")
            data.update({"title": title})
            data.update({"score": doc.get("score", 0.0)})
            data.update({"reranker_score": doc.get("reranker_score", 0.0)})
            data.update({"content": doc.get("content", "")})
            data.update({"docName": title})
            data.update({"docUrl": docUrl})
            data.update({"owner": doc.get("metadata_json", {}).get("owner", "")})
            data.update({"update_time": doc.get("metadata_json", {}).get("update_time", "")})
            data.update({"publish_time": doc.get("metadata_json", {}).get("publish_time", "")})
            data.update({"project_area": doc.get("metadata_json", {}).get("project_area", "")})
            data.update({"doc_type": doc.get("metadata_json", {}).get("doc_type", "")})
            docType = doc.get("metadata_json", {}).get("doc_type", "")
            if "xiaomi.f.mioffice.cn"in docUrl and docType in ["doc", "sheet"]:
                sheetName = doc.get("metadata_json", {}).get("Header 2", "")
                data.update({"sheetName": sheetName})
            formated_retrieved_docs.append(data)
            # print(f"data: {data}")
        return formated_retrieved_docs

    def _get_provider(self, enable_thinking: bool = True):
        """根据enable_thinking参数获取合适的provider"""
        return get_llm_provider(self.model_id, self.request_id, enable_thinking)
    
    async def _retrieve_knowledge(self, query: str, user_id: str, collection_name: str, top_k: int = None, min_score: float = None, use_reranker: bool = True):
        """
        检索单个库并重排，不做top_r过滤
        """
        self.logger.info(f"检索库: {collection_name}, 用户ID: {user_id}, 查询: {query}, top_k: {top_k}, min_score: {min_score}, use_reranker: {use_reranker}")
        search_start_time = time.time()
        search_results, error = await self.search_service.search(
            user_id=user_id,
            query=query,
            top_k=top_k,
            collection_name=collection_name
        )
        # 计算检索耗时
        search_elapsed_time = time.time() - search_start_time
        if error or not search_results:
            self.logger.error(f"检索库 {collection_name} 失败: {error}")
            return []
        self.logger.info(f"库 {collection_name} 检索到知识: {len(search_results)} 条，检索耗时: {search_elapsed_time:.3f}秒")
        # print(f"库 {collection_name} 检索结果: {search_results}")
        if use_reranker:
            # 记录重排开始时间
            rerank_start_time = time.time()
            reranked_docs = await self.rerank_service.rerank_with_prompt(
                query=query,
                documents=search_results,
                instruction=car_qa_rerank_prompt,
                top_r=top_k,
                min_score=min_score
            )
            # 计算重排耗时
            rerank_elapsed_time = time.time() - rerank_start_time
            self.logger.info(f"库 {collection_name} 重排后知识: {len(reranked_docs)} 条，重排耗时: {rerank_elapsed_time:.3f}秒")
        else:
            self.logger.info(f"跳过重排，直接使用原始检索结果")
            reranked_docs = search_results
        # 格式化重排后的文档
        format_reranked_docs = self.format_retrieved_docs(reranked_docs)
        # 为每个文档添加collection信息
        for doc in format_reranked_docs:
            doc["collection_name"] = collection_name
        self.logger.info(f"库 {collection_name} 格式化完成，共获取到 {len(format_reranked_docs)} 个文档")
        return format_reranked_docs

    def _get_user_department_collection(self, user_info: Optional[Dict[str, Any]]) -> Optional[str]:
        """
        根据用户信息获取对应的collection名称

        Args:
            user_info: 用户信息

        Returns:
            对应的collection名称，如果不匹配则返回None
        """
        if not user_info:
            return None

        # 使用四级部门信息
        dept_level4_desc = user_info.get("miDeptLevel5Desc", "")

        # 部门与collection的映射关系
        dept_collection_mapping = {
            "车身车间": "kb_car_cheshen_v2",
            "车身车间B1": "kb_car_cheshen_v2",
            "涂装车间": "kb_car_tuzhuang_v2",
            "涂装车间B1": "kb_car_tuzhuang_v2",
            "电池车间": "kb_car_dianchi_v2",
            "电池车间B1": "kb_car_dianchi_v2",
            "总装车间": "kb_car_zongzhuang_v2",
            "总装车间B1": "kb_car_zongzhuang_v2"
        }

        matched_collection = dept_collection_mapping.get(dept_level4_desc)
        if matched_collection:
            self.logger.info(f"用户四级部门 '{dept_level4_desc}' 匹配到collection: {matched_collection}")
        else:
            self.logger.info(f"用户四级部门 '{dept_level4_desc}' 未匹配到特定collection，使用默认排序")

        return matched_collection

    async def _retrieve_and_rerank_all_collections(self, query: str, user_id: str, top_k: int = None, top_r: int = None, min_score: float = None, use_reranker: bool = True, user_info: Optional[Dict[str, Any]] = None):
        """
        异步检索所有collection并重排，根据用户部门优先排序，最后统一排序取top_r
        """
        # 1. 调用query_rewrite_service对query进行改写
        rewrite_result = self.query_rewrite_service.rewrite_query(query)
        query_variants = rewrite_result.get("query_variants", [])

        # 2. 将改写后的query列表与原始query用空格拼接
        if query_variants:
            combined_query = "\n".join([query] + query_variants)
            self.logger.info(f"原始查询: {query}")
            self.logger.info(f"改写后的查询变体: {query_variants}")
            self.logger.info(f"拼接后的查询: {combined_query}")
        else:
            combined_query = query
            self.logger.info(f"未生成查询变体，使用原始查询: {query}")

        # 3. 使用拼接后的查询进行检索
        tasks = []
        collection_names = []
        for collection in CAR_SEARCH_COLLECTIONS:
            collection_name = collection.get("collection_name")
            if collection_name:
                collection_names.append(collection_name)
                tasks.append(self._retrieve_knowledge(combined_query, user_id, collection_name, top_k=top_k, min_score=min_score, use_reranker=use_reranker))
        all_results = await asyncio.gather(*tasks)

        # print(f"all_results: {all_results}")

        # 4. 根据用户部门进行优先排序
        user_dept_collection = self._get_user_department_collection(user_info)

        if user_dept_collection:
            # 按collection分组
            collection_results = {}
            for i, collection_name in enumerate(collection_names):
                collection_results[collection_name] = all_results[i]

            # 优先排序：用户部门对应的collection排在前面
            prioritized_docs = []
            other_docs = []

            # 先添加用户部门对应collection的结果
            if user_dept_collection in collection_results:
                prioritized_docs.extend(collection_results[user_dept_collection])
                self.logger.info(f"用户部门对应collection '{user_dept_collection}' 的结果数量: {len(collection_results[user_dept_collection])}")
            # print(f"prioritized_docs: {prioritized_docs}")
            # 再添加其他collection的结果
            for collection_name, docs in collection_results.items():
                if collection_name != user_dept_collection:
                    other_docs.extend(docs)
            # print(f"other_docs: {other_docs}")
            # 对其他collection的结果按reranker_score排序
            other_docs.sort(key=lambda x: x.get("reranker_score", 0), reverse=True)

            # 合并结果：优先部门的结果 + 其他部门按分数排序的结果
            merged_docs = prioritized_docs + other_docs

            self.logger.info(f"部门优先排序完成 - 优先部门结果: {len(prioritized_docs)} 条, 其他部门结果: {len(other_docs)} 条")
        else:
            # 如果用户部门不匹配，按原有逻辑处理
            merged_docs = [doc for docs in all_results for doc in docs]
            merged_docs.sort(key=lambda x: x.get("reranker_score", 0), reverse=True)
            # self.logger.info(f"用户部门未匹配，使用默认排序")

        # print(f"reranker_scores: {[doc.get('reranker_score', 0) for doc in merged_docs]}")

        # 5. 按top_r截断
        if top_r is not None:
            merged_docs = merged_docs[:top_r]
        self.logger.info(f"所有库合并后重排知识: {len(merged_docs)} 条, top_r: {top_r}")
        return merged_docs

    def format_knowledge(self, reranked_docs):
        formatted_docs = []
        for i, doc in enumerate(reranked_docs):
            formatted_doc = f"文档序号: {i+1}\n"
            formatted_doc += f"文档所属车间: {doc.get('project_area', '')}车间\n"
            formatted_doc += f"标题: {doc.get('title', '')}\n"
            formatted_doc += f"内容: {doc.get('content', '')}\n"
            # formatted_doc += f"章节名称: {doc.get('sheetName', '')}\n"
            formatted_doc += f"更新时间: {doc.get('update_time', '')}\n"
            formatted_docs.append(formatted_doc)
            reranked_docs[i]["refNum"] = i+1
        self.logger.info(f"格式化后的知识, 共: {len(formatted_docs)}条")
        return "\n\n".join(formatted_docs), reranked_docs
    
    def _build_messages(self, query: str, history: List[Dict], knowledge: str, mode: str = "common", user_info: Optional[Dict[str, Any]] = None) -> List[Dict]:
        """构建OpenAI格式的消息列表（适配新history格式）"""
        is_knowledge_empty = not knowledge or knowledge.strip() == ""

        if mode == "strict":
            if is_knowledge_empty:
                self.logger.info(f"strict模式下, 知识为空")
                sys_prompt = car_qa_strict_empty_sys_prompt
                user_prompt = car_qa_strict_empty_user_prompt
            else:
                self.logger.info(f"strict模式下, 知识不为空")
                sys_prompt = car_qa_strict_nonempty_sys_prompt
                user_prompt = car_qa_strict_nonempty_user_prompt
        else:  # common mode
            if is_knowledge_empty:
                self.logger.info(f"common模式下, 知识为空")
                sys_prompt = car_qa_common_empty_sys_prompt
                user_prompt = car_qa_common_empty_user_prompt
            else:
                self.logger.info(f"common模式下, 知识不为空")
                sys_prompt = car_qa_common_nonempty_sys_prompt
                user_prompt = car_qa_common_nonempty_user_prompt

        # 检查用户是否属于特定车间部门，且知识不为空
        workshop_priority_prompt = ""
        if not is_knowledge_empty and user_info:
            dept_level4_desc = user_info.get("miDeptLevel5Desc", "")
            target_departments = ["车身车间", "车身车间B1", "涂装车间", "涂装车间B1", "电池车间", "电池车间B1", "总装车间", "总装车间B1"]
            
            if dept_level4_desc in target_departments:
                # 提取车间名称（去掉B1后缀）
                workshop_name = dept_level4_desc.replace("B1", "")
                workshop_priority_prompt = f"\n\n特别注意：由于用户属于{workshop_name}部门，在回答问题时请优先使用文档所属车间为{workshop_name}的知识内容。"
                self.logger.info(f"用户属于特定车间 '{dept_level4_desc}'，添加车间知识优先使用提示")

        # 在系统提示词中添加车间优先提示
        final_sys_prompt = sys_prompt + workshop_priority_prompt

        messages = [{"role": "system", "content": final_sys_prompt}]
        for item in history[::-1]:
            if isinstance(item, dict) and "query" in item and "content" in item:
                messages.append({"role": "user", "content": item["query"]})
                messages.append({"role": "assistant", "content": item["content"]})
            else:
                self.logger.warning(f"跳过无效的历史记录项: {item}")
        formatted_query = user_prompt.replace("{{query}}", query).replace("{{body}}", knowledge)
        messages.append({"role": "user", "content": formatted_query})
        return messages
    
    def deduplicate_by_docurl(self,docs):
        seen = set()
        deduped = []
        for doc in docs:
            url = doc.get("docUrl")
            if url not in seen:
                deduped.append(doc)
                seen.add(url)
        return deduped
    
    async def generate_stream(
        self,
        query: str,
        user_id: str,
        history: List[Dict] = None,
        timeout: Optional[float] = None,
        top_k: int = None,
        top_r: int = None,
        min_score: float = None,
        conversation_id: Optional[str] = None,
        enable_thinking: bool = True,
        mode: str = "common",
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        use_reranker: bool = True,
        user_info: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        if history is None:
            self.logger.info(f"历史对话为空，开始新对话")
            history = []
            
                
        selected_user_id = ["cuixinrui1","kongshaoyang"]
        if user_id in selected_user_id:
            user_info = {"miDeptLevel5Desc": "车身车间"}
        
            
        self.logger.info(f"历史对话条数: {len(history)}")
        rerank_res = await self._retrieve_and_rerank_all_collections(query, user_id, top_k=top_k, top_r=top_r, min_score=min_score, use_reranker=use_reranker, user_info=user_info)
        knowledge, rerank_res = self.format_knowledge(rerank_res)
        self.logger.info(f"知识检索完成")
        self.logger.info(f"重排后的知识条数: {len(rerank_res)}")
        yield {"type": "reference", "content": json.dumps(rerank_res, ensure_ascii=False), "role": "", "finish_reason": ""}

        # 应用字符数长度限制
        limited_query, limited_history, limited_knowledge = self.char_limiter.limit_messages_for_rag_qa(
            query, history, knowledge
        )

        messages = self._build_messages(limited_query, limited_history, limited_knowledge, mode, user_info)
        # print(f"messages: {messages}")
        self.logger.info(f"开始调用模型服务")
        provider = self._get_provider(enable_thinking)
        # print(f"temp: {temperature}, top_p: {top_p}")
        async for chunk in provider.generate_stream(
            messages=messages,
            timeout=timeout,
            conversation_id=conversation_id,
            enable_thinking=enable_thinking,
            temperature=temperature,
            top_p=top_p,
            **kwargs
        ):
            yield chunk